import { Layout, Menu, Popconfirm } from 'antd'
import {
    HomeOutlined,
    DiffOutlined,
    EditOutlined,
    LogoutOutlined,
} from '@ant-design/icons'
import './index.scss'
import { Outlet, useLocation, useNavigate } from 'react-router'
import { useEffect } from 'react'
import type { MenuProps } from 'antd'
import { useUserProfile } from '@/hooks/useUser'

const { Header, Sider } = Layout

const items = [
    {
        label: '首页',
        key: '/',
        icon: <HomeOutlined />,
    },
    {
        label: '文章管理',
        key: '/article',
        icon: <DiffOutlined />,
    },
    {
        label: '创建文章',
        key: '/publish',
        icon: <EditOutlined />,
    },
]


const GeekLayout = () => {
    const { userProfile, getUserInfo, clearUserInfo } = useUserProfile()
    const navigate = useNavigate()
    const onMenuClick: MenuProps['onClick'] = (route) => {
        navigate(route.key)
    }

    // 反向高亮
    // 1. 获取当前路由路径
    const location = useLocation()
    const selectedkey = location.pathname


    useEffect(() => {
        getUserInfo()
    }, [])


    // 退出登录确认回调
    const onConfirm = () => {
        navigate('/login')
        clearUserInfo();
    }

    // const name = useSelector(state => state.user.userInfo.name)
    return (
        <Layout>
            <Header className="header">
                <div className="logo" />
                <div className="user-info">
                    <span className="user-name">{userProfile.name}</span>
                    <span className="user-logout">
                        <Popconfirm title="是否确认退出？" okText="退出" cancelText="取消" onConfirm={onConfirm}>
                            <LogoutOutlined /> 退出
                        </Popconfirm>
                    </span>
                </div>
            </Header>
            <Layout>
                <Sider width={200} className="site-layout-background">
                    <Menu
                        mode="inline"
                        theme="dark"
                        selectedKeys={[selectedkey]}
                        onClick={onMenuClick}
                        items={items}
                        style={{ height: '100%', borderRight: 0 }}></Menu>
                </Sider>
                <Layout className="layout-content" style={{ padding: 20 }}>
                    {/* 二级路由出口 */}
                    <Outlet />
                </Layout>
            </Layout>
        </Layout>
    )
}

export default GeekLayout